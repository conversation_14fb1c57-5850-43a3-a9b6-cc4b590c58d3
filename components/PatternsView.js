const { defineComponent, onMounted, inject } = Vue;
import SettingsAlert from './SettingsAlert.js';

export default defineComponent({
    name: 'PatternsView',
    components: {
        SettingsAlert
    },
    setup() {
        onMounted(() => {
        });

        return {};
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <sl-card class="content-card">
                <div slot="header">
                    <h2>句型句式</h2>
                </div>
                <p>学习各种英语句型结构和表达模式。</p>
            </sl-card>
        </div>
    `
});
