const { defineComponent, ref, onMounted, onUnmounted, nextTick, inject } = Vue;

export default defineComponent({
    name: 'OverviewView',
    setup() {
        // 全局 store
        const appStore = inject('appStore');

        const calendar = ref(null);
        let resizeObserver = null;

        const initCalendar = async () => {
            // 确保之前的日历实例已销毁
            if (calendar.value) {
                calendar.value.destroy();
            }

            await nextTick();

            // 初始化新的日历实例
            const calendarEl = document.getElementById('calendar');
            if (calendarEl && window.FullCalendar) {
                calendar.value = new FullCalendar.Calendar(calendarEl, {
                    timeZone: 'UTC',
                    initialView: 'dayGridMonth',
                    events: [
                        {
                            "title": "Meeting",
                            "start": "2025-06-18T10:30:00+00:00",
                            "end": "2025-06-18T12:30:00+00:00"
                        }
                    ],
                    editable: true,
                    selectable: true,
                    headerToolbar: {
                        right: 'prev,next',
                        center: 'title',
                        left: 'dayGridMonth,dayGridYear'
                    }
                });
                calendar.value.render();
            }
        };

        const destroyCalendar = () => {
            if (calendar.value) {
                calendar.value.destroy();
                calendar.value = null;
            }
        };

        onMounted(() => {
            initCalendar();
            // 监听 .content-view 尺寸变化，自动刷新 FullCalendar 布局
            const contentView = document.querySelector('.content-view');
            if (contentView && window.ResizeObserver) {
                resizeObserver = new ResizeObserver(() => {
                    if (calendar.value && typeof calendar.value.updateSize === 'function') {
                        calendar.value.updateSize();
                    }
                });
                resizeObserver.observe(contentView);
            }
        });

        onUnmounted(() => {
            destroyCalendar();
            // 断开 ResizeObserver
            if (resizeObserver) {
                resizeObserver.disconnect();
                resizeObserver = null;
            }
        });

        return {
            appStore,
            initCalendar,
            destroyCalendar
        };
    },
    template: `
        <div class="content-view">
            <sl-alert v-if="appStore.currentStudent === '' || appStore.currentBook === ''" variant="warning" open style="margin-bottom: 1.5rem;">
                <sl-icon slot="icon" name="exclamation-triangle"></sl-icon>
                <strong>请先设置学生和教材</strong>
                <div flex="dir:left cross:center">
                    <span>跳转到 "应用设置" 界面进行配置。</span>
                    <sl-button variant="text" @click="appStore.activeSection='settings'">知道了，点我马上跳转</sl-button>
                </div>
            </sl-alert>
            <!--
            <sl-card class="content-card">
                <div slot="header">
                    <h3>欢迎使用英语学习应用</h3>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); grid-auto-rows: 1fr; gap: 1rem;">
                    <sl-card style="height: 100%; display: flex; flex-direction: column;">
                        <div slot="header">每日任务</div>
                        <div style="flex: 1; display: flex; flex-direction: column; justify-content: space-between;">
                            <sl-progress-bar value="75" label="今日进度"></sl-progress-bar>
                            <br>
                            <p>今日已完成 2/4 个任务</p>
                        </div>
                    </sl-card>

                    <sl-card style="height: 100%; display: flex; flex-direction: column;">
                        <div slot="header">连续学习</div>
                        <div style="flex: 1; display: flex; flex-direction: column; justify-content: center; text-align: center;">
                            <h3 style="color: var(--sl-color-success-600); margin: 0; line-height: 28px;">7 天</h3>
                            <p style="margin: 0.5rem 0 0 0;">保持学习习惯</p>
                        </div>
                    </sl-card>

                    <sl-card style="height: 100%; display: flex; flex-direction: column;">
                        <div slot="header">词汇量</div>
                        <div style="flex: 1; display: flex; flex-direction: column; justify-content: center; text-align: center;">
                            <h3 style="color: var(--sl-color-primary-600); margin: 0; line-height: 28px;">1,250</h3>
                            <p style="margin: 0.5rem 0 0 0;">已掌握单词数量</p>
                        </div>
                    </sl-card>
                </div>
            </sl-card>
            -->

            <sl-card class="content-card">
                <div id="calendar"></div>
            </sl-card>
        </div>
    `
});
