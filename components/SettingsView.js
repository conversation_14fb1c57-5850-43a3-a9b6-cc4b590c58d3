const { defineComponent, ref, inject, nextTick, computed, onMounted } = Vue;

export default defineComponent({
    name: 'SettingsView',
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        const showContent = ref(false);
        const studentName = ref('');
        const addStudentDialog = ref(null);
        const toastAlert = ref(null);
        const toastMessage = ref('');
        
        function studentChange($event) {
            appStore.currentStudent = $event.target.value;
        }

        function bookChange($event) {
            appStore.currentBook = $event.target.value;
        }

        async function addStudent() {
            if (studentName.value === '') {
                toastMessage.value = '学生姓名不能为空';
                toastAlert.value.toast();
                return;
            }
            // 尝试写入数据
            try {
                await appStore.db.students.add({
                    name: studentName.value,
                    bookId: ''
                })
            }
            catch(ex) {
                toastMessage.value = ex.message;
                if (ex.message.includes('Key already exists in the object store')) toastMessage.value = '学生姓名已经存在！';
                toastAlert.value.toast();
                return;
            }
            // 刷新数据
            await appStore.loadMeta();
            studentName.value = '';
            addStudentDialog.value.hide();
        }

        onMounted(() => {
            // web-component 渲染有问题，这里先 hack 一下
            // 用延迟渲染的方式确保数据先于 web-component 到达
            setTimeout(() => {
                showContent.value = true;
            }, 100);
        })

        return {
            appStore,
            showContent,
            studentName,
            addStudent,
            addStudentDialog,
            studentChange,
            bookChange,
            toastAlert,
            toastMessage
        };

    },
    template: `
        <div class="content-view">
            <div v-if="showContent" flex="dir:left box:mean" style="gap: 1.5rem;">
                <sl-card class="content-card" :class="{ 'outline-1-danger': appStore.currentStudent === '', 'r-small': appStore.currentStudent === '' }">
                    <div slot="header">
                        <h3>学生选择</h3>
                    </div>
                    <div flex="dir:left box:last">
                        <!-- 选学生 -->
                        <sl-select :value="appStore.currentStudent" @sl-change="studentChange" size="small" :placeholder="appStore.students.length === 0 ? '请先添加学生' : '请选择当前学生'" :disabled="appStore.students.length === 0 ? '' : undefined">
                            <sl-option v-for="item in appStore.students" :value="item.name">{{ item.name }}</sl-option>
                        </sl-select>
                        <!-- 添加学生 -->
                        <sl-button variant="primary" size="small" @click="addStudentDialog.show()" style="margin-left: 8px;">添加学生</sl-button>
                    </div>
                    <!-- 添加弹框 -->
                    <sl-dialog ref="addStudentDialog" label="添加学生" style="--width: 300px;">
                        <sl-input :value="studentName" @sl-change="studentName = $event.target.value" autofocus placeholder="请输入学生姓名"></sl-input>
                        <sl-button @click="addStudent()" slot="footer" variant="primary">确定</sl-button>
                    </sl-dialog>
                </sl-card>
                <sl-card class="content-card" :class="{ 'outline-1-danger': appStore.currentBook === '', 'r-small': appStore.currentBook === '' }">
                    <div slot="header">
                        <h3>教材选择</h3>
                    </div>
                    <!-- 选教材 -->
                    <sl-select :value="appStore.currentBook" @sl-change="bookChange" size="small" :placeholder="appStore.books.length === 0 ? '请先添加教材' : '请选择当前教材'" :disabled="appStore.books.length === 0 ? '' : undefined" style="width: 100%;">
                        <sl-option v-for="item in appStore.books" :value="item.id">{{ item.name }}</sl-option>
                    </sl-select>
                </sl-card>
            </div>
            <sl-alert ref="toastAlert" variant="danger" duration="3000" closable>
                <sl-icon slot="icon" name="exclamation-octagon"></sl-icon>
                <strong>出错了</strong><br />
                {{ toastMessage }}
            </sl-alert>
        </div>
    `
});
