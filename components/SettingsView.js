const { defineComponent, ref, inject } = Vue;

export default defineComponent({
    name: 'SettingsView',
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        function studentChange() {
            console.log(appStore.currentStudent)
        }

        function bookChange() {
            console.log(appStore.currentBook)
        }

        return {
            appStore,
            bookChange,
        };

    },
    template: `
        <div class="content-view">
            <div flex="dir:left box:mean" style="gap: 15px;">
                <sl-card class="content-card">
                    <div slot="header">
                        <h3>学生选择</h3>
                    </div>
                    <!-- 选学生 -->
                    <sl-select :value="appStore.currentStudent" @sl-change="appStore.currentBook = $event.target.value; studentChange();" size="small" :placeholder="appStore.students.length === 0 ? '请先添加学生' : '请选择当前学生'" :disabled="appStore.currentStudent === '' ? '' : undefined" style="width: 260px;">
                        <sl-option v-for="item in appStore.students" :value="item.id">{{ item.name }}</sl-option>
                    </sl-select>
                </sl-card>
                <sl-card class="content-card">
                    <div slot="header">
                        <h3>教材选择</h3>
                    </div>
                    <!-- 选教材 -->
                    <sl-select :value="appStore.currentBook" @sl-change="appStore.currentBook = $event.target.value; bookChange();" size="small" :placeholder="appStore.books.length === 0 ? '请先添加教材' : '请选择当前教材'" :disabled="appStore.currentBook === '' ? '' : undefined" style="width: 260px;">
                        <sl-option v-for="item in appStore.books" :value="item.id">{{ item.name }}</sl-option>
                    </sl-select>
                </sl-card>
            </div>
        </div>
    `
});
