const { defineComponent, ref, reactive, computed, inject } = Vue;

export default defineComponent({
    name: 'WordCard',
    props: {
        data: {
            type: Object,
            required: true,
            default: () => ({
                word: '',
                pronunciation: { uk: '', us: '' },
                definitions: [],
                isPassed: false,
                isCollected: false
            })
        }
    },
    setup(props) {
        // 全局 store
        const appStore = inject('appStore');

        // 从 data 属性获取状态
        const isPassed = ref(props.data.isPassed || false);
        const isCollected = ref(props.data.isCollected || false);

        // State for revealing content
        const isPronunciationRevealed = ref(false);
        const revealedDefinitions = reactive({});

        // 计算是否所有块都已 revealed
        const allSectionsRevealed = computed(() => {
            const pronunciationRevealed = isPronunciationRevealed.value;
            const definitionsRevealed = props.data.definitions.every((_, index) => revealedDefinitions[index]);
            return pronunciationRevealed && definitionsRevealed;
        });

        // 计算按钮是否应该禁用
        const buttonsDisabled = computed(() => {
            // 如果初始状态都为 false，且不是所有块都 revealed，则禁用按钮
            const initiallyBothFalse = !props.data.isPassed && !props.data.isCollected;
            return initiallyBothFalse && !allSectionsRevealed.value;
        });

        const revealPronunciation = (event) => {
            event.stopPropagation();
            isPronunciationRevealed.value = true;
        };

        const revealDefinition = (index, event) => {
            event.stopPropagation();
            revealedDefinitions[index] = true;
        };

        const togglePassed = async () => {
            if (isPassed.value === true) await markWordAsUnpassed(props.data.word);
        };

        const toggleCollected = async () => {
            if (isCollected.value === true) await removeWordFromCollection(props.data.word);
        };

        const playPronunciation = (type) => {
            console.log(`Playing ${type} pronunciation for: ${props.data.word}`);
        };

        const addWordToCollection = async () => {
            console.log(`Adding ${props.data.word} to word set`);
            await appStore.wordsView.addWordToCollection(props.data.word);
            isCollected.value = true;
        };

        const removeWordFromCollection = async () => {
            console.log(`Removing ${props.data.word} from word set`);
            await appStore.wordsView.removeWordFromCollection(props.data.word);
            isCollected.value = false;
        };

        const markWordAsPassed = async () => {
            console.log(`Marked ${props.data.word} as learned`);
            await appStore.wordsView.markWordAsPassed(props.data.word);
            isPassed.value = true;
        };

        const markWordAsUnpassed = async () => {
            console.log(`Marked ${props.data.word} as unlearned`);
            await appStore.wordsView.markWordAsUnpassed(props.data.word);
            isPassed.value = false;
        };

        return {
            isPassed,
            isCollected,
            isPronunciationRevealed,
            revealedDefinitions,
            allSectionsRevealed,
            buttonsDisabled,
            revealPronunciation,
            revealDefinition,
            togglePassed,
            toggleCollected,
            playPronunciation,
            addWordToCollection,
            markWordAsPassed
        };
    },
    template: `
        <div class="word-card skeleton-shapes" :style="{
            borderColor: isPassed ? 'var(--sl-color-success-600)' :
                        isCollected ? 'var(--sl-color-warning-600)' :
                        'var(--sl-color-neutral-200)',
            backgroundColor: isPassed ? 'var(--sl-color-success-50)' :
                        isCollected ? 'var(--sl-color-warning-50)' :
                        '#fff',
            transition: 'border-color 0.2s ease'
        }">
            <!-- Word Header (always visible) -->
            <div class="word-header">
                <h3 class="word-title" :style="{
                    color: isPassed ? 'var(--sl-color-success-600)' :
                           isCollected ? 'var(--sl-color-warning-600)' :
                           'var(--sl-color-neutral-900)',
                    transition: 'color 0.2s ease'
                }">{{ data.word }}</h3>
                <div class="word-actions">
                    <sl-icon style="margin-top: -0.5px;"
                        :name="isCollected ? 'star-fill' : 'star'"
                        :class="{ 'favorite-active': isCollected }"
                        @click="toggleCollected"
                        :style="{ cursor: isCollected ? 'pointer' : 'default' }"
                    ></sl-icon>
                    <sl-icon
                        :name="isPassed ? 'check-circle-fill' : 'check-circle'"
                        :class="{ 'learned-active': isPassed }"
                        @click="togglePassed"
                        :style="{ cursor: isPassed ? 'pointer' : 'default' }"
                    ></sl-icon>
                </div>
            </div>

            <!-- Pronunciation Section -->
            <div @click="revealPronunciation" class="pronunciation-section" flex="dir:left cross:center box:first"
                 :style="{ cursor: isPronunciationRevealed ? 'default' : 'pointer' }">
                <sl-icon name="volume-up" class="definition-icon" style="width: 18px; height: 18px;"></sl-icon>
                <div flex="dir:left cross:center box:last" style="gap:0.3rem;">
                    <div>
                        <transition name="fade" mode="out-in">
                            <div v-if="isPronunciationRevealed" class="pronunciation-text">
                                <div v-if="data.pronunciation?.uk" class="pronunciation-item">英 {{ data.pronunciation.uk }}</div>
                                <div v-if="data.pronunciation?.us" class="pronunciation-item">美 {{ data.pronunciation.us }}</div>
                            </div>
                            <div v-else class="skeleton-placeholder">
                                <sl-skeleton effect="none" class="square" style="height: 41px; width: 100%;"></sl-skeleton>
                            </div>
                        </transition>
                    </div>
                    <div>
                        <transition name="fade" mode="out-in">
                            <sl-button v-if="isPronunciationRevealed" variant="default" size="small" circle @click="playPronunciation('detailed')" style="margin-left: auto;">
                                <sl-icon name="play"></sl-icon>
                            </sl-button>
                            <sl-skeleton v-else effect="none" style="width: 28px; height: 28px; border-radius: 50%; margin-left: auto;"></sl-skeleton>
                        </transition>
                    </div>
                </div>
            </div>

            <!-- Definitions Section -->
            <div class="definitions-section">
                <div v-for="(definition, index) in data.definitions"
                     @click="revealDefinition(index, $event)"
                     :key="index"
                     class="definition-item"
                     flex="dir:left box:first"
                     :style="{ cursor: revealedDefinitions[index] ? 'default' : 'pointer' }">
                    <!-- icon -->
                    <div style="padding-top: 8px;">
                        <sl-icon name="book" class="definition-icon"></sl-icon>
                    </div>
                    <!-- content -->
                    <transition name="fade" mode="out-in">
                        <div>
                            <!-- header -->
                            <div class="definition-header" flex="dir:left box:last">
                                <div v-if="revealedDefinitions[index]">
                                    <span class="part-of-speech">{{ definition.partOfSpeech }}</span>
                                    &nbsp;
                                    <span class="meaning">{{ definition.meaning }}</span>
                                </div>
                                <div v-else>
                                    <sl-skeleton effect="none" class="square" style="height: 30px; width: 100%;"></sl-skeleton>
                                </div>
                                <sl-button v-if="revealedDefinitions[index]" variant="default" size="small" circle class="play-btn">
                                    <sl-icon name="play"></sl-icon>
                                </sl-button>
                                <sl-skeleton v-else effect="none" style="width: 28px; height: 28px; border-radius: 50%; margin-left: auto;"></sl-skeleton>
                            </div>
                            <!-- example -->
                            <div v-if="definition.example" class="example-text">
                                <div v-if="revealedDefinitions[index]">
                                    {{ definition.example }}
                                    <span v-if="definition.exampleTranslation" class="example-translation"> {{ definition.exampleTranslation }}</span>
                                </div>
                                <sl-skeleton v-else effect="none" class="square" style="height: 44px; width: 100%;"></sl-skeleton>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>

            <!-- Footer Buttons (always visible) -->
            <div class="word-footer">
                <sl-button variant="success" size="small" @click="markWordAsPassed" class="footer-btn"
                           :disabled="isPassed || buttonsDisabled">我已学会</sl-button>
                <sl-button variant="warning" size="small" @click="addWordToCollection" class="footer-btn"
                           :disabled="isCollected || buttonsDisabled">加入关注</sl-button>
            </div>
        </div>
    `
});