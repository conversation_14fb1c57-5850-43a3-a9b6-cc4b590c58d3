const { defineComponent, ref, reactive } = Vue;

export default defineComponent({
    name: 'WordCard',
    props: {
        data: {
            type: Object,
            required: true,
            default: () => ({
                word: '',
                pronunciation: { uk: '', us: '' },
                definitions: [],
                examples: []
            })
        }
    },
    setup(props) {
        const isLearned = ref(false);
        const isFavorite = ref(false);

        // State for revealing content
        const isPronunciationRevealed = ref(false);
        const revealedDefinitions = reactive({});

        const revealPronunciation = () => isPronunciationRevealed.value = true;
        const revealDefinition = (index) => revealedDefinitions[index] = true;

        const toggleLearned = () => {
            if (isLearned.value === true) isLearned.value = false;
        };

        const toggleFavorite = () => {
            if (isFavorite.value === true) isFavorite.value = false;
        };

        const playPronunciation = (type) => {
            console.log(`Playing ${type} pronunciation for: ${props.data.word}`);
        };

        const addToWordSet = () => {
            if (isLearned.value) isLearned.value = false;
            isFavorite.value = true;
            console.log(`Adding ${props.data.word} to word set`);
        };

        const markAsLearned = () => {
            if (isFavorite.value) isFavorite.value = false;
            isLearned.value = true;
            console.log(`Marked ${props.data.word} as learned`);
        };

        return {
            isLearned,
            isFavorite,
            isPronunciationRevealed,
            revealedDefinitions,
            revealPronunciation,
            revealDefinition,
            toggleLearned,
            toggleFavorite,
            playPronunciation,
            addToWordSet,
            markAsLearned
        };
    },
    template: `
        <div class="word-card skeleton-shapes" :style="{
            borderColor: isLearned ? 'var(--sl-color-success-600)' :
                        isFavorite ? 'var(--sl-color-warning-600)' :
                        'var(--sl-color-neutral-200)',
            backgroundColor: isLearned ? 'var(--sl-color-success-50)' :
                        isFavorite ? 'var(--sl-color-warning-50)' :
                        '#fff',
            transition: 'border-color 0.2s ease'
        }">
            <!-- Word Header (always visible) -->
            <div class="word-header">
                <h3 class="word-title" :style="{
                    color: isLearned ? 'var(--sl-color-success-600)' :
                           isFavorite ? 'var(--sl-color-warning-600)' :
                           'var(--sl-color-neutral-900)',
                    transition: 'color 0.2s ease'
                }">{{ data.word }}</h3>
                <div class="word-actions">
                    <sl-icon style="margin-top: -0.5px;"
                        :name="isFavorite ? 'star-fill' : 'star'"
                        :class="{ 'favorite-active': isFavorite }"
                        @click="toggleFavorite"
                        :style="{ cursor: isFavorite ? 'pointer' : 'default' }"
                    ></sl-icon>
                    <sl-icon
                        :name="isLearned ? 'check-circle-fill' : 'check-circle'"
                        :class="{ 'learned-active': isLearned }"
                        @click="toggleLearned"
                        :style="{ cursor: isLearned ? 'pointer' : 'default' }"
                    ></sl-icon>
                </div>
            </div>

            <!-- Pronunciation Section -->
            <div @click="revealPronunciation" class="pronunciation-section" flex="dir:left cross:center box:first">
                <sl-icon name="volume-up" class="definition-icon" style="width: 18px; height: 18px;"></sl-icon>
                <div flex="dir:left cross:center box:last" style="gap:0.3rem;">
                    <div>
                        <transition name="fade" mode="out-in">
                            <div v-if="isPronunciationRevealed" class="pronunciation-text">
                                <div v-if="data.pronunciation?.uk" class="pronunciation-item">英 [{{ data.pronunciation.uk }}]</div>
                                <div v-if="data.pronunciation?.us" class="pronunciation-item">美 [{{ data.pronunciation.us }}]</div>
                            </div>
                            <div v-else class="skeleton-placeholder">
                                <sl-skeleton effect="none" class="square" style="height: 41px; width: 100%;"></sl-skeleton>
                            </div>
                        </transition>
                    </div>
                    <div>
                        <transition name="fade" mode="out-in">
                            <sl-button v-if="isPronunciationRevealed" variant="default" size="small" circle @click="playPronunciation('detailed')" style="margin-left: auto;">
                                <sl-icon name="play"></sl-icon>
                            </sl-button>
                            <sl-skeleton v-else effect="none" style="width: 28px; height: 28px; border-radius: 50%; margin-left: auto;"></sl-skeleton>
                        </transition>
                    </div>
                </div>
            </div>

            <!-- Definitions Section -->
            <div class="definitions-section">
                <div v-for="(definition, index) in data.definitions"  @click="revealDefinition(index)" :key="index" class="definition-item" flex="dir:left box:first">
                    <!-- icon -->
                    <div style="padding-top: 8px;">
                        <sl-icon name="book" class="definition-icon"></sl-icon>
                    </div>
                    <!-- content -->
                    <transition name="fade" mode="out-in">
                        <div>
                            <!-- header -->
                            <div class="definition-header" flex="dir:left box:last">
                                <div v-if="revealedDefinitions[index]">
                                    <span class="part-of-speech">{{ definition.partOfSpeech }}</span>
                                    &nbsp;&nbsp;
                                    <span class="meaning">{{ definition.meaning }}</span>
                                </div>
                                <div v-else>
                                    <sl-skeleton effect="none" class="square" style="height: 30px; width: 100%;"></sl-skeleton>
                                </div>
                                <sl-button v-if="revealedDefinitions[index]" variant="default" size="small" circle class="play-btn">
                                    <sl-icon name="play"></sl-icon>
                                </sl-button>
                                <sl-skeleton v-else effect="none" style="width: 28px; height: 28px; border-radius: 50%; margin-left: auto;"></sl-skeleton>
                            </div>
                            <!-- example -->
                            <div v-if="definition.example" class="example-text">
                                <div v-if="revealedDefinitions[index]">
                                    {{ definition.example }}
                                    <span v-if="definition.exampleTranslation" class="example-translation">（{{ definition.exampleTranslation }}）</span>
                                </div>
                                <sl-skeleton v-else effect="none" class="square" style="height: 44px; width: 100%;"></sl-skeleton>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>

            <!-- Footer Buttons (always visible) -->
            <div class="word-footer">
                <sl-button variant="success" size="small" @click="markAsLearned" class="footer-btn" :disabled="isLearned">我已学会</sl-button>
                <sl-button variant="warning" size="small" @click="addToWordSet" class="footer-btn" :disabled="isFavorite">加入关注</sl-button>
            </div>
        </div>
    `
});