const { defineComponent, ref, inject, onMounted, nextTick, watch } = Vue;

export default defineComponent({
    name: 'Toolbar',
    props: {
        showShadow: {
            type: Boolean,
            default: false
        },
        toggleSidebar: {
            type: Function,
            required: false
        }
    },
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        // 单词 view 当前行为
        const wordsViewAction = ref('');
        function wordsViewActionChange(e) {
            wordsViewAction.value = e.target.value;
            emit(`words.${e.target.value}.show`);
        }

        // 触发 cmd-fire 事件，返回字符串
        function emit(name) {
            appStore.wordsViewStatus = name.replace('words.', '');
            context.emit('cmd-fire', name);
        }

        async function exit() {
            wordsViewAction.value = '';
            appStore.wordsViewStatus = '';
        }

        onMounted(async () => {
            // console.log('toolbar mounted', appStore.currentBook);
        });


        return {
            emit,
            appStore,
            wordsViewAction,
            wordsViewActionChange,
            exit,
            emit,
        };
    },
    template: `
        <div class="toolbar" :class="{ 'toolbar--shadow': showShadow }">
            <!-- 边栏开关按钮 -->
            <sl-icon-button name="list" label="Menu" @click="toggleSidebar && toggleSidebar()"></sl-icon-button>

            <!-- 概况工具栏 -->
            <template v-if="appStore.activeSection === 'overview'">
            
            </template>

            <!-- 单词专项工具栏 -->
            <template v-if="appStore.activeSection === 'words'">
                <!-- 选动作 -->
                <sl-radio-group :value="wordsViewAction" @sl-change="wordsViewActionChange" size="small">
                    <sl-radio-button value="practice" :disabled="(appStore.currentBook === '' || appStore.wordsViewStatus === 'test.start') ? '' : undefined">
                        <sl-icon name="book" slot="prefix"></sl-icon>
                        练习
                    </sl-radio-button>
                    <sl-radio-button value="test" :disabled="(appStore.currentBook === '' || appStore.wordsViewStatus === 'practice.start' || appStore.wordsViewStatus === 'practice.refresh') ? '' : undefined">
                        <sl-icon name="pen" slot="prefix"></sl-icon>
                        测验
                    </sl-radio-button>
                    <sl-radio-button value="collection" :disabled="(appStore.currentBook === '' || appStore.wordsViewStatus.includes('.start') || appStore.wordsViewStatus === 'practice.refresh') ? '' : undefined">
                        <sl-icon name="journal-x" slot="prefix"></sl-icon>
                        重点关注
                    </sl-radio-button>
                </sl-radio-group>
                <!-- 练习控制 -->
                <template v-if="wordsViewAction === 'practice'">
                    <sl-button-group size="small">
                        <sl-button v-if="appStore.wordsViewStatus === 'practice.show' || appStore.wordsViewStatus === 'practice.stop'" variant="success" size="small" @click="emit('words.practice.start')">
                            开始
                        </sl-button>
                        <sl-button v-if="appStore.wordsViewStatus === 'practice.start' || appStore.wordsViewStatus === 'practice.refresh'" variant="success" size="small" @click="emit('words.practice.refresh')">
                            刷新
                        </sl-button>
                        <sl-button v-if="appStore.wordsViewStatus === 'practice.start' || appStore.wordsViewStatus === 'practice.refresh'" variant="warning" size="small" @click="emit('words.practice.stop')">
                            结束
                        </sl-button>
                        <sl-button v-if="appStore.wordsViewStatus === 'practice.show' || appStore.wordsViewStatus === 'practice.stop'" variant="warning" size="small" @click="exit">
                            退出
                        </sl-button>
                    </sl-button-group>
                </template>
                <!-- 测试控制 -->
                <template v-if="wordsViewAction === 'test'">
                    <sl-button-group size="small">
                        <sl-button v-if="appStore.wordsViewStatus === 'test.show' || appStore.wordsViewStatus === 'test.submit'" variant="success" size="small" @click="emit('words.test.start')">
                            开始
                        </sl-button>
                        <sl-button v-if="appStore.wordsViewStatus === 'test.start'" variant="warning" size="small" @click="emit('words.test.submit')">
                            交卷
                        </sl-button>
                        <sl-button v-if="appStore.wordsViewStatus === 'test.show' || appStore.wordsViewStatus === 'test.submit'" variant="warning" size="small" @click="exit">
                            退出
                        </sl-button>
                    </sl-button-group>
                </template>
                <!-- 重点关注控制 -->
                <template v-if="wordsViewAction === 'collection'">
                    <sl-button-group size="small">
                        <sl-button v-if="appStore.wordsViewStatus === 'collection.show'" variant="warning" size="small" @click="exit">
                            退出
                        </sl-button>
                    </sl-button-group>
                </template>
            </template>
            <div style="flex: 1;"></div>
            <sl-tag v-if="appStore.timerSpan !== ''" pill variant="danger" style="width: 104px; text-align: center;">
                <h3>{{ appStore.timerSpan }}</h3>
            </sl-tag>
            <sl-input v-else pill
                placeholder="搜索" 
                size="small" 
                style="width: 100px;"
            >
            </sl-input>
                
            <!-- <sl-button variant="default" size="small" circle @click="">
                <sl-icon name="bell"></sl-icon>
            </sl-button>
            <sl-dropdown distance="10">
                <sl-button slot="trigger" variant="default" size="small" circle>
                    <sl-icon name="person-circle"></sl-icon>
                </sl-button>
                <sl-menu>
                    <sl-menu-item>个人资料</sl-menu-item>
                    <sl-menu-item>账户设置</sl-menu-item>
                    <sl-divider></sl-divider>
                    <sl-menu-item>退出登录</sl-menu-item>
                </sl-menu>
            </sl-dropdown> -->
        </div>
    `
});
