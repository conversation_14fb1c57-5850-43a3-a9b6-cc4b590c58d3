const { defineComponent, ref, inject, onMounted, nextTick, watch } = Vue;

export default defineComponent({
    name: 'Toolbar',
    props: {
        showShadow: {
            type: Boolean,
            default: false
        },
        toggleSidebar: {
            type: Function,
            required: false
        }
    },
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');
        // 监控 view 变化
        watch(() => appStore.activeSection, async (val) => {
            // console.log('activeSection', val)
            // 单词专项界面，控制工具栏
            if (val === 'words') {
            }
        })
        // 单词 view 当前行为
        const wordsViewAction = ref('');
        function wordsViewActionChange(e) {
            // console.log(e.target.value);
            wordsViewAction.value = e.target.value;
            emit(`words.${e.target.value}.show`);
        }

        // 触发 cmd-fire 事件，返回字符串
        function emit(name) {
            appStore.wordsViewStatus = name.replace('words.', '');
            context.emit('cmd-fire', name);
        }

        onMounted(async () => {
            // console.log('toolbar mounted', appStore.currentBook);
        });


        return {
            emit,
            appStore,
            wordsViewAction,
            wordsViewActionChange,
            emit,
        };
    },
    template: `
        <div class="toolbar" :class="{ 'toolbar--shadow': showShadow }">
            <!-- 边栏开关按钮 -->
            <sl-icon-button name="list" label="Menu" @click="toggleSidebar && toggleSidebar()"></sl-icon-button>

            <!-- 概况工具栏 -->
            <template v-if="appStore.activeSection === 'overview'">
            
            </template>

            <!-- 单词专项工具栏 -->
            <template v-if="appStore.activeSection === 'words'">
                <!-- 选动作 -->
                <sl-radio-group :value="wordsViewAction" @sl-change="wordsViewActionChange" size="small">
                    <sl-radio-button class="actions" value="practice" :disabled="appStore.currentBook === '' ? '' : undefined">
                        <sl-icon name="book" slot="prefix"></sl-icon>
                        练习
                    </sl-radio-button>
                    <sl-radio-button class="actions" value="test" :disabled="appStore.currentBook === '' ? '' : undefined">
                        <sl-icon name="pen" slot="prefix"></sl-icon>
                        测验
                    </sl-radio-button>
                    <sl-radio-button class="actions" value="collection" :disabled="appStore.currentBook === '' ? '' : undefined">
                        <sl-icon name="journal-x" slot="prefix"></sl-icon>
                        错题集
                    </sl-radio-button>
                </sl-radio-group>
                <!-- 练习控制 -->
                <template v-if="wordsViewAction === 'practice'">
                    <sl-button-group size="small">
                        <sl-button :disabled="appStore.wordsViewStatus === 'practice.start' || appStore.wordsViewStatus === 'practice.refresh'" variant="success" size="small" @click="emit('words.practice.start')">
                            开始
                        </sl-button>
                        <sl-button :disabled="appStore.wordsViewStatus === 'practice.show' || appStore.wordsViewStatus === 'practice.stop'" variant="primary" size="small" @click="emit('words.practice.refresh')">
                            刷新
                        </sl-button>
                        <sl-button :disabled="appStore.wordsViewStatus === 'practice.show' || appStore.wordsViewStatus === 'practice.stop'" variant="warning" size="small" @click="emit('words.practice.stop')">
                            结束
                        </sl-button>
                    </sl-button-group>
                </template>
                <!-- 测试控制 -->
                <template v-if="wordsViewAction === 'test'">
                    <sl-button-group size="small">
                        <sl-button :disabled="appStore.wordsViewStatus === 'test.start'" variant="success" size="small" @click="emit('words.test.start')">
                            开始
                        </sl-button>
                        <sl-button :disabled="appStore.wordsViewStatus === 'test.show' || appStore.wordsViewStatus === 'test.submit'" variant="warning" size="small" @click="emit('words.test.submit')">
                            交卷
                        </sl-button>
                    </sl-button-group>
                </template>
            </template>
            <div style="flex: 1;"></div>
            <div flex="dir:left main:right cross:center">
                <sl-input pill
                    placeholder="搜索" 
                    size="small" 
                    style="width: 80px; margin-right: 6px;"
                >
                </sl-input>
                <sl-button variant="default" size="small" circle @click="">
                    <sl-icon name="search"></sl-icon>
                </sl-button>
            </div>
                
            <!-- <sl-button variant="default" size="small" circle @click="">
                <sl-icon name="bell"></sl-icon>
            </sl-button>
            <sl-dropdown distance="10">
                <sl-button slot="trigger" variant="default" size="small" circle>
                    <sl-icon name="person-circle"></sl-icon>
                </sl-button>
                <sl-menu>
                    <sl-menu-item>个人资料</sl-menu-item>
                    <sl-menu-item>账户设置</sl-menu-item>
                    <sl-divider></sl-divider>
                    <sl-menu-item>退出登录</sl-menu-item>
                </sl-menu>
            </sl-dropdown> -->
        </div>
    `
});
