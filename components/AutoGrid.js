const { defineComponent, onMounted, inject } = Vue;

export default defineComponent({
    name: 'AutoGrid',
    props: {
        minCols: {
            type: Number,
            default: 1
        },
        maxCols: {
            type: Number,
            default: 8
        },
        minWidth: {
            type: Number,
            required: true,
            default: 150
        },
        maxWidth: {
            type: Number,
            required: true,
            default: 300
        },
        gap: {
            type: Number,
            default: 16
        }
    },
    setup(props, { emit }) {
        const { ref, computed, watch, onMounted, onUnmounted } = Vue;

        const containerRef = ref(null);
        const columnCount = ref(1);
        const actualWidth = ref(0);
        const containerWidth = ref(0);

        // 计算网格布局
        const calculateLayout = () => {
            if (!containerRef.value) return;

            // 获取容器宽度
            containerWidth.value = containerRef.value.clientWidth;

            // 计算每列的实际宽度
            let bestColumnCount = props.minCols;
            let bestWidth = 0;

            // 寻找最佳列数
            for (let columns = props.minCols; columns <= props.maxCols; columns++) {
                // 计算当前列数下的宽度（减去间距）
                const width = (containerWidth.value - (columns - 1) * props.gap) / columns;
                // console.log(columns, width, props.minWidth, props.maxWidth);
                // 检查宽度是否在范围内
                if (width >= props.minWidth && width <= props.maxWidth) {
                    // 如果宽度更接近最大宽度，则选择此列数
                    if (Math.abs(width - props.maxWidth) < Math.abs(bestWidth - props.maxWidth) || bestWidth === 0) {
                        bestColumnCount = columns;
                        bestWidth = width;
                    }
                }
            }

            // 如果没有找到合适的列数，使用更贴近最小宽度的值
            if (bestWidth === 0 && bestColumnCount === props.minCols) {
                bestColumnCount = Math.floor(containerWidth.value / props.minWidth);
                bestWidth = (containerWidth.value - (bestColumnCount - 1) * props.gap) / bestColumnCount;
                if (bestWidth < props.minWidth) {
                    bestColumnCount--;
                    bestWidth = (containerWidth.value - (bestColumnCount - 1) * props.gap) / bestColumnCount;
                }
                // console.log('接近值', bestColumnCount, bestWidth, props.minWidth, props.maxWidth);
            }

            columnCount.value = bestColumnCount;
            actualWidth.value = bestWidth;
        };

        // 使用 ResizeObserver 监听容器大小变化
        let resizeObserver = null;

        onMounted(() => {
            calculateLayout();

            // 初始化 ResizeObserver
            resizeObserver = new ResizeObserver(entries => {
                for (let entry of entries) {
                    if (entry.target === containerRef.value) {
                        calculateLayout();
                    }
                }
            });

            if (containerRef.value) {
                resizeObserver.observe(containerRef.value);
            }
        });

        onUnmounted(() => {
            if (resizeObserver && containerRef.value) {
                resizeObserver.unobserve(containerRef.value);
            }
        });

        // 监听属性变化
        watch(() => [props.minWidth, props.maxWidth, props.gap], calculateLayout);

        // 计算容器样式
        const containerStyle = computed(() => {
            return {
                display: 'grid',
                'grid-template-columns': `repeat(${columnCount.value}, ${actualWidth.value}px)`,
                gap: `${props.gap}px`,
                'justify-content': 'center',
                width: '100%',
                height: '100%',
            };
        });

        // 返回布局统计信息
        const gridStats = computed(() => ({
            columns: columnCount.value,
            actualWidth: Math.round(actualWidth.value),
            containerWidth: containerWidth.value,
            totalItems: containerRef.value ? containerRef.value.children.length : 0
        }));

        // 将统计信息发送给父组件
        watch(gridStats, (newStats) => {
            emit('stats-update', newStats);
        }, { deep: true, immediate: true });

        return {
            containerRef,
            containerStyle,
            gridStats
        };
    },
    template: `
        <div ref="containerRef" :style="containerStyle">
          <slot></slot>
        </div>
      `
});