const { defineComponent, onMounted, inject } = Vue;
import SettingsAlert from './SettingsAlert.js';

export default defineComponent({
    name: 'PronunciationView',
    components: {
        SettingsAlert
    },
    setup() {
        onMounted(() => {
        });

        return {};
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <sl-card class="content-card">
                <div slot="header">
                    <h2>基础发音</h2>
                </div>
                <p>学习英语音标、发音规则和语音技巧。</p>
                <br>
                <sl-button variant="primary">开始发音练习</sl-button>
            </sl-card>
        </div>
    `
});
