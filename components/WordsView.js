const { defineComponent, onMounted, inject } = Vue;

export default defineComponent({
    name: 'WordsView',
    setup() {
        // 全局 store
        const appStore = inject('appStore');

        onMounted(() => {
        });

        return {
            appStore,
        };
    },
    template: `
        <div class="content-view">
            <sl-alert v-if="appStore.currentStudent === '' || appStore.currentBook === ''" variant="warning" open style="margin-bottom: 1.5rem;">
                <sl-icon slot="icon" name="exclamation-triangle"></sl-icon>
                <strong>请先设置学生和教材</strong>
                <div flex="dir:left cross:center">
                    <span>跳转到 "应用设置" 界面进行配置。</span>
                    <sl-button variant="text" @click="appStore.activeSection='settings'">知道了，点我马上跳转</sl-button>
                </div>
            </sl-alert>
            <sl-card class="content-card">
                <div slot="header">
                    <h2>单词专项</h2>
                </div>
                <p>系统化学习英语单词，包括词汇记忆、拼写和用法。</p>
                <br>
                <table>
                    <thead>
                        <tr>
                            <th>单词</th>
                            <th>释义</th>
                            <th>难度</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>apple</td>
                            <td>苹果</td>
                            <td><sl-badge variant="success">简单</sl-badge></td>
                            <td><sl-badge variant="primary">已掌握</sl-badge></td>
                            <td>
                                <sl-button size="small" variant="text">编辑</sl-button>
                                <sl-button size="small" variant="text">删除</sl-button>
                            </td>
                        </tr>
                        <tr>
                            <td>beautiful</td>
                            <td>美丽的</td>
                            <td><sl-badge variant="warning">中等</sl-badge></td>
                            <td><sl-badge variant="neutral">学习中</sl-badge></td>
                            <td>
                                <sl-button size="small" variant="text">编辑</sl-button>
                                <sl-button size="small" variant="text">删除</sl-button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </sl-card>
        </div>
    `
});
