const { defineComponent, onMounted, inject } = Vue;
import SettingsAlert from './SettingsAlert.js';
import AutoGrid from './AutoGrid.js';

export default defineComponent({
    name: 'WordsView',
    components: {
        SettingsAlert,
        AutoGrid
    },
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        onMounted(() => {
        });

        return {
            appStore,
        };
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <div v-if="appStore.wordsViewStatus === 'words.practice.start'" style="padding: 2px;">
                <AutoGrid :min-width="260" :max-width="340" :gap="20">
                    <div v-for="item in [1,2,3,4,5,6,7,8,9,10]" :style="{ height: '140px' }" class="bg-primary-light">{{ item }}</div>
                </AutoGrid>
            </div>
        </div>
    `
});
