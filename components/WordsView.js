const { defineComponent, onMounted, inject, ref, watch, nextTick } = Vue;
import SettingsAlert from './SettingsAlert.js';
import AutoGrid from './AutoGrid.js';
import WordCard from './WordCard.js';

export default defineComponent({
    name: 'WordsView',
    components: {
        SettingsAlert,
        AutoGrid,
        WordCard
    },
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        // 监控 status 给列表赋值
        watch(() => appStore.wordsView.status, async (nv, ov) => {
            // console.log(nv, ov)
            if (nv.includes('practice.start_') || nv.includes('practice.refresh_')) {
                const words = await appStore.wordsView.fetchPracticeWords();
                // console.log('刷新单词', words);
                appStore.wordsView.practiceWords = words;
            }
        })

        onMounted(async () => {
        });

        return {
            appStore,
        };
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <div v-if="appStore.wordsView.status.includes('practice.start_') || appStore.wordsView.status.includes('practice.refresh_')" style="padding: 2px;">
                <AutoGrid :min-width="260" :max-width="340" :gap="20">
                    <div v-for="item in appStore.wordsView.practiceWords" :key="item.word" :style="{ height: 'auto' }">
                        <!-- 单词卡片示例 -->
                        <WordCard :data="item" />
                    </div>
                </AutoGrid>
            </div>

        </div>
    `
});
