const { defineComponent, onMounted, inject, ref } = Vue;
import SettingsAlert from './SettingsAlert.js';
import AutoGrid from './AutoGrid.js';
import WordCard from './WordCard.js';

export default defineComponent({
    name: 'WordsView',
    components: {
        SettingsAlert,
        AutoGrid,
        WordCard
    },
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        // 示例单词数据
        const sampleWordData = ref({
            word: 'cook',
            pronunciation: {
                uk: 'kʊk',
                us: 'kʊk'
            },
            definitions: [
                {
                    partOfSpeech: 'n. 名词',
                    meaning: '厨师',
                    example: 'The cook is making food.',
                    exampleTranslation: '厨师正在做食物。'
                },
                {
                    partOfSpeech: 'v. 动词',
                    meaning: '烹饪',
                    example: 'I can cook noodles.',
                    exampleTranslation: '我会煮面条。'
                }
            ]
        });

        onMounted(() => {
        });

        return {
            appStore,
            sampleWordData
        };
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <div v-if="appStore.wordsViewStatus === 'practice.start'" style="padding: 2px;">
                <AutoGrid :min-width="260" :max-width="340" :gap="20">
                    <div v-for="item in [1,2,3,4,5,6]" :style="{ height: 'auto' }">
                        <!-- 单词卡片示例 -->
                        <WordCard :data="sampleWordData" />
                    </div>
                </AutoGrid>
            </div>

        </div>
    `
});
