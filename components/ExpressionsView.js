const { defineComponent, onMounted, inject } = Vue;
import SettingsAlert from './SettingsAlert.js';

export default defineComponent({
    name: 'ExpressionsView',
    components: {
        SettingsAlert
    },
    setup() {
        onMounted(() => {
        });

        return {};
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <sl-card class="content-card">
                <div slot="header">
                    <h2>常用语句</h2>
                </div>
                <p>掌握日常交流中的常用表达和句子。</p>
            </sl-card>
        </div>
    `
});
