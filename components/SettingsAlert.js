const { defineComponent, inject } = Vue;

export default defineComponent({
    name: 'SettingsAlert',
    setup() {
        // 全局 store
        const appStore = inject('appStore');

        return {
            appStore,
        };
    },
    template: `
        <sl-alert v-if="appStore.currentStudent === '' || appStore.currentBook === ''" variant="warning" open style="margin-bottom: 1.5rem;">
            <sl-icon slot="icon" name="exclamation-triangle"></sl-icon>
            <strong>请先设置学生和教材</strong>
            <div flex="dir:left cross:center">
                <span>跳转到 "应用设置" 界面进行配置。</span>
                <sl-button variant="text" @click="appStore.activeSection='settings'">知道了，点我马上跳转</sl-button>
            </div>
        </sl-alert>
    `
});
