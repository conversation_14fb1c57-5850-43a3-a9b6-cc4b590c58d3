const { defineComponent, onMounted, inject } = Vue;

export default defineComponent({
    name: 'PhrasesView',
    setup(props, context) {
        // 全局 store
        const appStore = inject('appStore');

        onMounted(() => {
        });

        return {
            appStore,
        };
    },
    template: `
        <div class="content-view">
            <sl-alert v-if="appStore.currentStudent === '' || appStore.currentBook === ''" variant="warning" open style="margin-bottom: 1.5rem;">
                <sl-icon slot="icon" name="exclamation-triangle"></sl-icon>
                <strong>请先设置学生和教材</strong>
                <div flex="dir:left cross:center">
                    <span>跳转到 "应用设置" 界面进行配置。</span>
                    <sl-button variant="text" @click="appStore.activeSection='settings'">知道了，点我马上跳转</sl-button>
                </div>
            </sl-alert>
            <sl-card class="content-card">
                <div slot="header">
                    <h2>短语专项</h2>
                </div>
                <p>学习常用英语短语和固定搭配。</p>
            </sl-card>
        </div>
    `
});
