const { defineComponent, onMounted, inject } = Vue;
import SettingsAlert from './SettingsAlert.js';

export default defineComponent({
    name: 'PhrasesView',
    components: {
        SettingsAlert
    },
    setup() {
        onMounted(() => {
        });

        return {};
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <sl-card class="content-card">
                <div slot="header">
                    <h2>短语专项</h2>
                </div>
                <p>学习常用英语短语和固定搭配。</p>
            </sl-card>
        </div>
    `
});
