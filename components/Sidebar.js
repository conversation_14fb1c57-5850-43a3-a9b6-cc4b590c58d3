const { defineComponent, inject } = Vue;

export default defineComponent({
    name: 'Sidebar',
    setup() {

        const appStore = inject('appStore');

        const setActiveSection = (section) => {
            appStore.activeSection = section;
        };

        const isActive = (section) => {
            return appStore.activeSection === section;
        };

        return {
            appStore,
            setActiveSection,
            isActive
        };
    },
    template: `
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">English App</div>
            </div>
            <div class="sidebar-content">
                <nav>
                    <div 
                        v-for="item in appStore.navigationItems" 
                        :key="item.id" 
                        class="nav-item"
                    >
                        <sl-button 
                            variant="text" 
                            size="medium"
                            @click="setActiveSection(item.id)"
                            :class="{ 'active': isActive(item.id) }"
                        >
                            <sl-icon :name="item.icon" slot="prefix"></sl-icon>
                            {{ item.label }}
                        </sl-button>
                    </div>
                </nav>
            </div>
        </div>
    `
});
