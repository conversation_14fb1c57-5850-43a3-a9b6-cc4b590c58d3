const { defineComponent, onMounted, inject } = Vue;
import SettingsAlert from './SettingsAlert.js';

export default defineComponent({
    name: 'GrammarView',
    components: {
        SettingsAlert
    },
    setup() {
        onMounted(() => {
        });

        return {};
    },
    template: `
        <div class="content-view">
            <SettingsAlert />
            <sl-card class="content-card">
                <div slot="header">
                    <h2>语法专项</h2>
                </div>
                <p>系统学习英语语法规则和语法结构。</p>
            </sl-card>
        </div>
    `
});
