/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 应用主容器 */
#app {
    height: 100vh;
    min-width: 768px;
    display: flex;
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 160px;
    background: var(--sl-color-neutral-50);
    border-right: 1px solid var(--sl-color-neutral-200);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.sidebar-header {
    height: 60px;
    padding: 1rem;
    border-bottom: 1px solid var(--sl-color-neutral-200);
    background: var(--sl-color-neutral-100);
    text-align: center;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.toolbar {
    height: 60px;
    background: var(--sl-color-neutral-0);
    border-bottom: 1px solid var(--sl-color-neutral-200);
    display: flex;
    align-items: center;
    padding: 0 1rem;
    gap: 1rem;
    flex-shrink: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: box-shadow 0.3s ease;
}

.toolbar--shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem 1rem;
    background: var(--sl-color-neutral-0);
}

.content-view {
    margin: auto;
    max-width: 1000px;
}

/* 导航项样式 */
.nav-item {
    margin-bottom: 0.5rem;
}

.nav-item sl-button {
    width: 100%;
    justify-content: flex-start;
    text-align: left;
}

.nav-item sl-button.active {
    background-color: var(--sl-color-primary-50);
    color: var(--sl-color-primary-700);
    border-left: 3px solid var(--sl-color-primary-600);
}

.nav-item sl-button.active::part(base) {
    background-color: var(--sl-color-primary-50);
    color: var(--sl-color-primary-700);
}

/* 内容卡片样式 */
.content-card {
    margin-bottom: 1rem;
    width: 100%;
}

/* Logo 样式 */
.logo {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--sl-color-primary-600);
}

/* 保证 FullCalendar 自适应父容器高度，防止出现多余滚动条 */
#calendar {
    padding: 4px;
    height: 100%;
    min-height: 500px;
    box-sizing: border-box;
    overflow: hidden;
}

.outline-1-primary { outline: var(--sl-color-primary-600) 1px solid; }
.outline-2-primary { outline: var(--sl-color-primary-600) 2px solid; }
.outline-1-success { outline: var(--sl-color-success-600) 1px solid; }
.outline-2-success { outline: var(--sl-color-success-600) 2px solid; }
.outline-1-warning { outline: var(--sl-color-warning-600) 1px solid; }
.outline-2-warning { outline: var(--sl-color-warning-600) 2px solid; }
.outline-1-danger { outline: var(--sl-color-danger-600) 1px solid; }
.outline-2-danger { outline: var(--sl-color-danger-600) 2px solid; }

.r-small { border-radius: var(--sl-border-radius-small); }
.r-medium { border-radius: var(--sl-border-radius-medium); }
.r-large { border-radius: var(--sl-border-radius-large); }

.bg-primary-light { background: var(--sl-color-primary-200); }
.bg-success-light { background: var(--sl-color-success-200); }
.bg-warning-light { background: var(--sl-color-warning-200); }
.bg-danger-light { background: var(--sl-color-danger-200); }
