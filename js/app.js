const { createApp, ref, reactive, computed, onMounted, provide } = Vue;
const { useStorage } = VueUse;

// 导入组件
import Sidebar from '../components/Sidebar.js';
import Toolbar from '../components/Toolbar.js';
import OverviewView from '../components/OverviewView.js';
import PronunciationView from '../components/PronunciationView.js';
import WordsView from '../components/WordsView.js';
import PhrasesView from '../components/PhrasesView.js';
import ExpressionsView from '../components/ExpressionsView.js';
import GrammarView from '../components/GrammarView.js';
import PatternsView from '../components/PatternsView.js';
import SettingsView from '../components/SettingsView.js';

const App = {
    components: {
        Sidebar,
        Toolbar,
        OverviewView,
        PronunciationView,
        WordsView,
        PhrasesView,
        ExpressionsView,
        GrammarView,
        PatternsView,
        SettingsView
    },
    setup() {

        const students = useStorage('students', []);
        const currentStudent = useStorage('currentStudent', '');
        const books = useStorage('books', [
            {
                id: 'lk3a',
                name: '鲁科版三年级上学期'
            },
            {
                id: 'lk3b',
                name: '鲁科版三年级下学期'
            }
        ]);
        const currentBook = useStorage('currentBook', '');
        
        // 设置一个全局响应式对象
        const appStore = reactive({
            activeSection: 'overview',
            showToolbarShadow: false,
            showSidebar: true,
            navigationItems: [
                { id: 'overview', label: '学习概况', icon: 'pie-chart' },
                { id: 'pronunciation', label: '基础发音', icon: 'mic' },
                { id: 'words', label: '单词专项', icon: 'book' },
                { id: 'phrases', label: '短语专项', icon: 'collection' },
                { id: 'expressions', label: '常用语句', icon: 'chat-dots' },
                { id: 'grammar', label: '语法专项', icon: 'code-square' },
                { id: 'patterns', label: '句型句式', icon: 'list-check' },
                { id: 'settings', label: '应用设置', icon: 'gear' }
            ],
            students,
            currentStudent,
            books,
            currentBook,
            wordsViewStatus: '',
        });
        // 然后通过 provide 模拟成全局 store
        provide('appStore', appStore);

        // 计算属性
        const currentComponent = computed(() => {
            const componentMap = {
                'overview': 'OverviewView',
                'pronunciation': 'PronunciationView',
                'words': 'WordsView',
                'phrases': 'PhrasesView',
                'expressions': 'ExpressionsView',
                'grammar': 'GrammarView',
                'patterns': 'PatternsView',
                'settings': 'SettingsView'
            };
            return componentMap[appStore.activeSection] || 'OverviewView';
        });

        // 方法
        const handleScroll = (event) => {
            const scrollTop = event.target.scrollTop;
            appStore.showToolbarShadow = scrollTop > 0;
        };

        const cmdFire = (name) => {
            console.log(name)
        }

        // 切换侧边栏显示
        const toggleSidebar = () => {
            appStore.showSidebar = !appStore.showSidebar;
        };

        // 生命周期
        onMounted(() => {
            // 添加滚动事件监听
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.addEventListener('scroll', handleScroll);
            }
        });

        return {
            appStore,
            currentComponent,
            handleScroll,
            toggleSidebar,
            cmdFire,
        };
    },
    template: `
        <div id="app">
            <Sidebar v-if="appStore.showSidebar" />
            <div class="main-content">
                <Toolbar @cmd-fire="cmdFire" :show-shadow="appStore.showToolbarShadow" :toggle-sidebar="toggleSidebar" />
                <div class="content-area" @scroll="handleScroll">
                    <component :is="currentComponent" />
                </div>
            </div>
        </div>
    `
};

// 创建并挂载应用
const app = createApp(App);

// 配置自定义元素
app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('sl-') || tag === 'full-calendar';

app.mount('#wrapper');
