const { defineComponent, ref, reactive, computed, onMounted, provide } = Vue;
const { useStorage } = VueUse;
import { _importData, _fetchPracticeWords, _addWordToCollection, _markWordAsPassed, _removeWordFromCollection, _markWordAsUnpassed } from './data.js';

// 导入组件
import Sidebar from '../components/Sidebar.js';
import Toolbar from '../components/Toolbar.js';
import SettingsAlert from '../components/SettingsAlert.js';
import WordCard from '../components/WordCard.js';
import OverviewView from '../components/OverviewView.js';
import PronunciationView from '../components/PronunciationView.js';
import WordsView from '../components/WordsView.js';
import PhrasesView from '../components/PhrasesView.js';
import ExpressionsView from '../components/ExpressionsView.js';
import GrammarView from '../components/GrammarView.js';
import PatternsView from '../components/PatternsView.js';
import SettingsView from '../components/SettingsView.js';

export default defineComponent({
    name: 'App',
    components: {
        Sidebar,
        Toolbar,
        SettingsAlert,
        WordCard,
        OverviewView,
        PronunciationView,
        WordsView,
        PhrasesView,
        ExpressionsView,
        GrammarView,
        PatternsView,
        SettingsView
    },
    setup() {

        // 同步保存到 localStorage 的数据
        const activeSection = useStorage('activeSection', 'overview');
        const currentStudent = useStorage('currentStudent', '');
        const currentBook = useStorage('currentBook', '');

        // 设置一个全局响应式对象
        const dbInstance = new Dexie('KidsEnglish');

        // 初始化前端数据库
        async function initDB(db) {
            // 定义结构
            db.version(1).stores(
                {
                    // 学生
                    students: '++name, bookId',
                    // 教材
                    books: '++id, name',
                    // 单词
                    words: '++word, bookId, pronunciation, definitions',
                    // 已掌握
                    passed: '++id, type, student, bookId, target, [student+bookId], [student+bookId+target]',
                    // 重点关注
                    collected: '++id, type, student, bookId, target, [student+bookId], [student+bookId+target]',
                    // 学习日志
                    logs: '++id, student, bookId, day, from, to, duration, [student+bookId], [student+bookId+day]',
                }
            );
            // 检查数据库是否存在
            const exists = await Dexie.exists(db.name);
            if (!exists) {
                console.log('数据库不存在，执行初始化');
                await _importData(db);
            }
            await db.open();
            await loadMeta(db);
        };

        // 加载应用元数据
        async function loadMeta(db) {
            if (!db) db = dbInstance;
            // 填充 appStore 数据
            appStore.books = await db.books.toArray();
            // console.log('从数据库中读取 books 成功：', appStore.books);
            appStore.students = await db.students.toArray();
            // console.log('从数据库中读取 students 成功：', appStore.students);
        }

        // 全局 store
        const appStore = reactive({
            db: dbInstance,
            activeSection,
            showToolbarShadow: false,
            showSidebar: true,
            navigationItems: [
                { id: 'overview', label: '学习概况', icon: 'pie-chart' },
                { id: 'pronunciation', label: '基础发音', icon: 'mic' },
                { id: 'words', label: '单词专项', icon: 'book' },
                { id: 'phrases', label: '短语专项', icon: 'collection' },
                { id: 'expressions', label: '常用语句', icon: 'chat-dots' },
                { id: 'grammar', label: '语法专项', icon: 'code-square' },
                { id: 'patterns', label: '句型句式', icon: 'list-check' },
                { id: 'settings', label: '应用设置', icon: 'gear' }
            ],
            loadMeta,
            students: [],
            currentStudent,
            books: [],
            currentBook,
            timer: null,
            timerEvent: '',
            timerStart: null,
            timerSpan: '',
            fetchPracticeWords,
            startTimer: (timerEvent) => {
                // 启动计时器
                appStore.timerSpan = '00:00:00';
                appStore.timerEvent = timerEvent;
                appStore.timerStart = (new Date()).getTime();
                appStore.timer = setInterval(() => {
                    const now = new Date();
                    const offset = Math.floor((now.getTime() - appStore.timerStart) / 1000);
                    appStore.timerSpan = totimerSpan(offset);
                }, 1000);
            },
            resetTimer: () => {
                // 重置计数器
                clearInterval(appStore.timer);
                appStore.timer = null;
                appStore.timerEvent = '';
                appStore.timerStart = null;
                appStore.timerSpan = '';
            },
            wordsView: {
                status: '',
                practiceWords: [],
                fetchPracticeWords: async (num = 6) => {
                    return await _fetchPracticeWords(appStore.db, appStore.currentStudent, appStore.currentBook, num);
                },
                addWordToCollection: async (target) => {
                    await _addWordToCollection(appStore.db, appStore.currentStudent, appStore.currentBook, target);
                },
                markWordAsPassed: async (target) => {
                    await _markWordAsPassed(appStore.db, appStore.currentStudent, appStore.currentBook, target);
                },
                removeWordFromCollection: async (target) => {
                    await _removeWordFromCollection(appStore.db, appStore.currentStudent, appStore.currentBook, target);
                },
                markWordAsUnpassed: async (target) => {
                    await _markWordAsUnpassed(appStore.db, appStore.currentStudent, appStore.currentBook, target);
                },
            },
        });

        // 然后通过 provide 模拟成全局 store
        provide('appStore', appStore);

        // 计算属性
        const currentComponent = computed(() => {
            const componentMap = {
                'overview': 'OverviewView',
                'pronunciation': 'PronunciationView',
                'words': 'WordsView',
                'phrases': 'PhrasesView',
                'expressions': 'ExpressionsView',
                'grammar': 'GrammarView',
                'patterns': 'PatternsView',
                'settings': 'SettingsView'
            };
            return componentMap[appStore.activeSection] || 'OverviewView';
        });

        // 方法
        function handleScroll(event) {
            const scrollTop = event.target.scrollTop;
            appStore.showToolbarShadow = scrollTop > 0;
        };

        // 秒换算成经过时间
        function totimerSpan(seconds) {
            // 计算小时、分钟和剩余秒数
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            // 使用padStart确保两位数格式
            const formattedHours = String(hours).padStart(2, '0');
            const formattedMinutes = String(minutes).padStart(2, '0');
            const formattedSeconds = String(secs).padStart(2, '0');
            // 返回结果
            return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
        }

        // 触发命令（通常来自顶部工具栏）
        function cmdFire(name) {
            console.log(name)
            if (name.startsWith('words.')) {
                // appStore.wordsView.status = name.replace('words.', '');
                if (name.includes('.start')) appStore.startTimer(name.split('.')[1]);
                if (name.includes('.stop') || name.includes('.submit')) appStore.resetTimer();
            }
        }

        // 切换侧边栏显示
        function toggleSidebar() {
            appStore.showSidebar = !appStore.showSidebar;
        };

        // 获得练习单词列表
        async function fetchPracticeWords(num = 6) {
            return await _fetchPracticeWords(appStore.db, appStore.currentStudent, appStore.currentBook, num);
        }

        // 生命周期
        onMounted(async () => {
            // 添加滚动事件监听
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.addEventListener('scroll', handleScroll);
            }
            // 数据库初始化
            await initDB(dbInstance);
            // console.log(appStore)
        });

        return {
            appStore,
            currentComponent,
            handleScroll,
            toggleSidebar,
            cmdFire,
        };
    },
    template: `
        <div id="app">
            <Sidebar v-if="appStore.showSidebar" />
            <div class="main-content">
                <Toolbar @cmd-fire="cmdFire" :show-shadow="appStore.showToolbarShadow" :toggle-sidebar="toggleSidebar" />
                <div class="content-area" @scroll="handleScroll">
                    <component :is="currentComponent" />
                </div>
            </div>
        </div>
    `
});
