// 导入数据（初始化前端数据）
export async function _importData(db) {
    // 初始化学生数据
    await db.students.clear();
    await db.students.bulkPut([
        {
            name: '窦乙竣',
            bookId: '',
        }
    ]);
    // 初始化教材数据
    await db.books.clear();
    await db.books.bulkPut([
        {
            id: 'lk3a',
            name: '鲁科版三年级上学期'
        },
        {
            id: 'lk3b',
            name: '鲁科版三年级下学期'
        }
    ]);
    // 初始化单词数据
    await db.words.clear();
    // 加载 lk3a.json
    const lk3aResponse = await fetch('/data/lk3a.json');
    const lk3aWords = await lk3aResponse.json();
    // console.log(lk3aWords);
    await db.words.bulkPut(lk3aWords);
    // 加载 lk3b.json
    const lk3bResponse = await fetch('/data/lk3b.json');
    const lk3bWords = await lk3bResponse.json();
    // console.log(lk3bWords);
    await db.words.bulkPut(lk3bWords);
    console.log('数据导入成功')
}

// 随机获取训练用单词列表
export async function _fetchPracticeWords(db, student, bookId, num = 6) {
    // 从 collected 表中获取 student 和 bookId 相同的记录
    const collected = await db.collected.where({
        student,
        bookId
    }).toArray();
    // console.log('重点关注的单词', collected);
    // 从 passed 表中获取 student 和 bookId 相同的记录
    const passed = await db.passed.where({
        student,
        bookId
    }).toArray();
    // console.log('已掌握的单词', passed);
    // 将 collected 和 passed 数据合并
    const merged = [...collected, ...passed];
    // console.log('合并后的排除单词', merged);
    // 从 words 表中获取所有单词
    const allWords = await db.words.where({
        bookId
    }).toArray();
    // console.log('所有单词', allWords);
    // 创建备选单词表（默认是全部单词）
    let candidateWords = [];
    // 如果重点关注和已掌握占比小于 30% 则只选择干净单词
    let rate = merged.length / allWords.length;
    if (rate < 0.3) {
        candidateWords = allWords.filter(word => {
            return !merged.find(item => item.target === word.word);
        });
    }
    // 如果占比大于等于 30% 小于 70% 则允许加入重点关注单词
    // 也就是只排除已掌握的单词
    if (rate >= 0.3 && rate < 0.7) {
        candidateWords = allWords.filter(word => {
            return !passed.find(item => item.target === word.word);
        });
    }
    // 如果占比大于等于 70% 则允许全部单词
    if (rate >= 0.7) candidateWords = allWords;
    // console.log('备选单词', candidateWords);
    // 随机获取 num 个单词
    const randomWords = candidateWords.sort(() => 0.5 - Math.random()).slice(0, num);
    // 为 randomWords 中的每个单词添加 isLearned 和 isPassed 字段，注意这两个字段的值分别来自 passed 和 collected
    for (let word of randomWords) {
        word.isPassed = passed.find(item => item.target === word.word) ? true : false;
        word.isCollected = collected.find(item => item.target === word.word) ? true : false;
    }
    // console.log('生成单词', randomWords);
    return randomWords;
}

// 将单词加入重点关注
export async function _addWordToCollection(db, student, bookId, target) {
    await db.transaction('rw', db.passed, db.collected, async () => {
        // 如果单词在“已掌握”中，先从中删除
        await db.passed.where({ student, bookId, target }).delete();
        // 检查是否已存在重复数据，不存在则加入
        const exists = await db.collected.where({
            student,
            bookId,
            target
        }).count();
        if (exists === 0) {
            await db.collected.add({
                student,
                bookId,
                target
            });
        }
    });
}

// 将单词从重点关注中移除
export async function _removeWordFromCollection(db, student, bookId, target) {
    try {
        await db.collected.where({
            student,
            bookId,
            target
        }).delete();
    }
    catch (ex) {
        console.log(ex.message)
    }
}

// 将单词标记为已学会
export async function _markWordAsPassed(db, student, bookId, target) {
    await db.transaction('rw', db.collected, db.passed, async () => {
        // 如果单词在“重点关注”中，先从中删除
        await db.collected.where({ student, bookId, target }).delete();
        // 检查是否已存在重复数据，不存在则加入
        const exists = await db.passed.where({
            student,
            bookId,
            target
        }).count();
        if (exists === 0) {
            await db.passed.add({
                student,
                bookId,
                target
            });
        }
    });
}

// 将单词从已学会列表中移除
export async function _markWordAsUnpassed(db, student, bookId, target) {
    try {
        await db.passed.where({
            student,
            bookId,
            target
        }).delete();
    }
    catch (ex) {
        console.log(ex.message)
    }
}