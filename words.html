<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Words Application</title>
    
    <!-- Shoelace UI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.15.1/cdn/themes/light.css" />
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Shoelace UI JS -->
    <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.15.1/cdn/shoelace-autoloader.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #app {
            height: 100vh;
            display: flex;
            overflow: hidden;
        }
        
        .sidebar {
            width: 250px;
            background: var(--sl-color-neutral-50);
            border-right: 1px solid var(--sl-color-neutral-200);
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }
        
        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid var(--sl-color-neutral-200);
            background: var(--sl-color-neutral-100);
        }
        
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .toolbar {
            height: 60px;
            background: var(--sl-color-neutral-0);
            border-bottom: 1px solid var(--sl-color-neutral-200);
            display: flex;
            align-items: center;
            padding: 0 1rem;
            gap: 1rem;
            flex-shrink: 0;
        }
        
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: var(--sl-color-neutral-0);
        }
        
        .nav-item {
            margin-bottom: 0.5rem;
        }
        
        .nav-item sl-button {
            width: 100%;
            justify-content: flex-start;
        }
        
        .content-card {
            margin-bottom: 1rem;
        }
        
        .logo {
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--sl-color-primary-600);
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">Words App</div>
            </div>
            <div class="sidebar-content">
                <nav>
                    <div class="nav-item">
                        <sl-button 
                            variant="text" 
                            size="medium"
                            @click="setActiveSection('dashboard')"
                            :class="{ 'active': activeSection === 'dashboard' }">
                            <sl-icon name="house" slot="prefix"></sl-icon>
                            仪表板
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button 
                            variant="text" 
                            size="medium"
                            @click="setActiveSection('words')"
                            :class="{ 'active': activeSection === 'words' }">
                            <sl-icon name="book" slot="prefix"></sl-icon>
                            单词管理
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button 
                            variant="text" 
                            size="medium"
                            @click="setActiveSection('practice')"
                            :class="{ 'active': activeSection === 'practice' }">
                            <sl-icon name="play-circle" slot="prefix"></sl-icon>
                            练习模式
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button 
                            variant="text" 
                            size="medium"
                            @click="setActiveSection('statistics')"
                            :class="{ 'active': activeSection === 'statistics' }">
                            <sl-icon name="bar-chart" slot="prefix"></sl-icon>
                            统计分析
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button 
                            variant="text" 
                            size="medium"
                            @click="setActiveSection('settings')"
                            :class="{ 'active': activeSection === 'settings' }">
                            <sl-icon name="gear" slot="prefix"></sl-icon>
                            设置
                        </sl-button>
                    </div>
                </nav>
            </div>
        </div>
        
        <!-- 右侧主要内容区 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <div class="toolbar">
                <sl-button variant="primary" size="small">
                    <sl-icon name="plus" slot="prefix"></sl-icon>
                    新建
                </sl-button>
                <sl-button variant="default" size="small">
                    <sl-icon name="upload" slot="prefix"></sl-icon>
                    导入
                </sl-button>
                <sl-button variant="default" size="small">
                    <sl-icon name="download" slot="prefix"></sl-icon>
                    导出
                </sl-button>
                <div style="flex: 1;"></div>
                <sl-input 
                    placeholder="搜索..." 
                    size="small" 
                    style="width: 200px;"
                    v-model="searchQuery">
                    <sl-icon name="search" slot="prefix"></sl-icon>
                </sl-input>
                <sl-button variant="default" size="small" circle>
                    <sl-icon name="bell"></sl-icon>
                </sl-button>
                <sl-dropdown>
                    <sl-button slot="trigger" variant="default" size="small" circle>
                        <sl-icon name="person-circle"></sl-icon>
                    </sl-button>
                    <sl-menu>
                        <sl-menu-item>个人资料</sl-menu-item>
                        <sl-menu-item>账户设置</sl-menu-item>
                        <sl-divider></sl-divider>
                        <sl-menu-item>退出登录</sl-menu-item>
                    </sl-menu>
                </sl-dropdown>
            </div>
            
            <!-- 内容展示区 -->
            <div class="content-area">
                <!-- 仪表板内容 -->
                <div v-if="activeSection === 'dashboard'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>欢迎使用 Words 应用</h2>
                        </div>
                        <p>这是一个基于 Vue 3 和 Shoelace UI 构建的单词学习应用。</p>
                        <br>
                        <sl-button variant="primary">开始学习</sl-button>
                    </sl-card>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                        <sl-card>
                            <div slot="header">学习统计</div>
                            <sl-progress-bar value="75" label="今日进度"></sl-progress-bar>
                            <br>
                            <p>今日已学习 15/20 个单词</p>
                        </sl-card>
                        
                        <sl-card>
                            <div slot="header">词汇量</div>
                            <h3 style="color: var(--sl-color-primary-600);">1,250</h3>
                            <p>已掌握单词数量</p>
                        </sl-card>
                        
                        <sl-card>
                            <div slot="header">连续学习</div>
                            <h3 style="color: var(--sl-color-success-600);">7 天</h3>
                            <p>保持学习习惯</p>
                        </sl-card>
                    </div>
                </div>
                
                <!-- 单词管理内容 -->
                <div v-if="activeSection === 'words'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>单词管理</h2>
                        </div>
                        <p>在这里管理您的单词库，添加、编辑或删除单词。</p>
                        <br>
                        <sl-table>
                            <table>
                                <thead>
                                    <tr>
                                        <th>单词</th>
                                        <th>释义</th>
                                        <th>难度</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>apple</td>
                                        <td>苹果</td>
                                        <td><sl-badge variant="success">简单</sl-badge></td>
                                        <td><sl-badge variant="primary">已掌握</sl-badge></td>
                                        <td>
                                            <sl-button size="small" variant="text">编辑</sl-button>
                                            <sl-button size="small" variant="text">删除</sl-button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>beautiful</td>
                                        <td>美丽的</td>
                                        <td><sl-badge variant="warning">中等</sl-badge></td>
                                        <td><sl-badge variant="neutral">学习中</sl-badge></td>
                                        <td>
                                            <sl-button size="small" variant="text">编辑</sl-button>
                                            <sl-button size="small" variant="text">删除</sl-button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </sl-table>
                    </sl-card>
                </div>
                
                <!-- 其他内容区域 -->
                <div v-if="activeSection === 'practice'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>练习模式</h2>
                        </div>
                        <p>选择练习模式开始学习单词。</p>
                    </sl-card>
                </div>
                
                <div v-if="activeSection === 'statistics'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>统计分析</h2>
                        </div>
                        <p>查看您的学习统计和进度分析。</p>
                    </sl-card>
                </div>
                
                <div v-if="activeSection === 'settings'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>设置</h2>
                        </div>
                        <p>配置应用设置和个人偏好。</p>
                    </sl-card>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    activeSection: 'dashboard',
                    searchQuery: ''
                }
            },
            methods: {
                setActiveSection(section) {
                    this.activeSection = section;
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
