<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4CAF50',
                        secondary: '#5B9BD5',
                        accent: '#F08080',
                        neutral: '#9E9E9E',
                        gold: '#E6C35C',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto max-w-5xl bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- 顶部导航栏 -->
        <div class="flex items-center justify-between p-4 border-b">
            <div class="flex items-center space-x-4">
                <!-- LOGO -->
                <div class="text-2xl font-bold text-primary">LOGO</div>
                <!-- 问候语 -->
                <div class="flex items-center space-x-2">
                    <span class="text-primary text-xl">@</span>
                    <span>下午好，麦乙俊</span>
                </div>
            </div>
            
            <!-- 右侧选择区域 -->
            <div class="flex items-center space-x-3">
                <!-- 学习内容切换区 -->
                <div class="relative">
                    <select class="appearance-none bg-white border rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary">
                        <option>小学三年级 (上)</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                        </svg>
                    </div>
                </div>
                
                <div class="relative">
                    <select class="appearance-none bg-white border rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary">
                        <option>英语</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                        </svg>
                    </div>
                </div>
                
                <div class="relative">
                    <select class="appearance-none bg-white border rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary">
                        <option>章节五回顾</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                        </svg>
                    </div>
                </div>
                
                <!-- 用户头像 -->
                <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center cursor-pointer hover:bg-gray-300 transition-colors">
                    <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="p-6">
            <!-- 练习标题和错题复习按钮 -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold">练习</h1>
                <button class="bg-gold text-white px-6 py-2 rounded-md hover:bg-yellow-500 transition-colors group relative">
                    错题复习
                    <span class="absolute right-0 top-0 -mt-1 -mr-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </button>
            </div>
            
            <!-- 练习类型选择 -->
            <div class="mb-8">
                <!-- 第一行 -->
                <div class="flex space-x-4 mb-4">
                    <button class="rounded-full bg-neutral text-white px-6 py-3 flex-1 max-w-xs hover:bg-gray-600 transition-colors">
                        基础
                    </button>
                    <button class="rounded-md bg-teal-200 text-teal-800 px-6 py-3 flex-1 max-w-xs hover:bg-teal-300 transition-colors">
                        字母
                    </button>
                    <button class="rounded-md bg-secondary text-white px-6 py-3 flex-1 max-w-xs hover:bg-blue-600 transition-colors">
                        音标
                    </button>
                    <button class="rounded-md bg-accent text-white px-6 py-3 flex-1 max-w-xs hover:bg-red-400 transition-colors">
                        发音训练
                    </button>
                </div>
                
                <!-- 第二行 -->
                <div class="flex space-x-4">
                    <button class="rounded-full bg-neutral text-white px-6 py-3 flex-1 max-w-xs hover:bg-gray-600 transition-colors">
                        专项
                    </button>
                    <button class="rounded-md border border-gray-300 bg-white text-gray-700 px-6 py-3 flex-1 max-w-xs hover:bg-gray-100 transition-colors">
                        单词
                    </button>
                    <button class="rounded-md border border-gray-300 bg-white text-gray-700 px-6 py-3 flex-1 max-w-xs hover:bg-gray-100 transition-colors">
                        短语
                    </button>
                    <button class="rounded-md border border-gray-300 bg-white text-gray-700 px-6 py-3 flex-1 max-w-xs hover:bg-gray-100 transition-colors">
                        句式
                    </button>
                    <button class="rounded-md border border-gray-300 bg-white text-gray-700 px-6 py-3 flex-1 max-w-xs hover:bg-gray-100 transition-colors">
                        语法
                    </button>
                </div>
            </div>
            
            <!-- 生成测试按钮 -->
            <div class="flex justify-center mb-8">
                <button class="bg-primary text-white px-12 py-4 rounded-md text-lg font-medium hover:bg-green-600 transition-colors w-1/3">
                    生成测试
                </button>
            </div>
            
            <!-- 底部记录区域 -->
            <div class="grid grid-cols-2 gap-6">
                <div>
                    <h2 class="text-xl font-bold mb-4">打卡记录</h2>
                    <div class="border rounded-md h-64 p-4">
                        <!-- 日历内容将在这里 -->
                    </div>
                </div>
                <div>
                    <h2 class="text-xl font-bold mb-4">学习日志</h2>
                    <div class="border rounded-md h-64 p-4">
                        <!-- 表格内容将在这里 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 用户头像悬停显示弹出菜单
            const userAvatar = document.querySelector('.rounded-full.bg-gray-200');
            userAvatar.addEventListener('mouseenter', function() {
                // 这里可以添加显示弹出菜单的代码
                console.log('用户头像悬停');
            });
            
            // 下拉菜单交互
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    console.log('选择了:', this.value);
                });
            });
        });
    </script>
</body>
</html>