<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Playground</title>

    <!-- Shoelace UI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/themes/light.css" />

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Shoelace UI JS -->
    <script type="module"
        src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/shoelace-autoloader.js"></script>
    <script type="module"
        src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/components/icon/icon.js"></script>
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/core@6.1.17/index.global.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/web-component@6.1.17/index.global.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@6.1.17/index.global.min.js'></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,
        body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #app {
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 160px;
            background: var(--sl-color-neutral-50);
            border-right: 1px solid var(--sl-color-neutral-200);
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .sidebar-header {
            height: 60px;
            padding: 1rem;
            border-bottom: 1px solid var(--sl-color-neutral-200);
            background: var(--sl-color-neutral-100);
            text-align: center;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .toolbar {
            height: 60px;
            background: var(--sl-color-neutral-0);
            border-bottom: 1px solid var(--sl-color-neutral-200);
            display: flex;
            align-items: center;
            padding: 0 1rem;
            gap: 1rem;
            flex-shrink: 0;
        }

        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: var(--sl-color-neutral-0);
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-item sl-button {
            width: 100%;
            justify-content: flex-start;
            text-align: left;
        }

        .nav-item sl-button.active {
            background-color: var(--sl-color-primary-50);
            color: var(--sl-color-primary-700);
            border-left: 3px solid var(--sl-color-primary-600);
        }

        .nav-item sl-button.active::part(base) {
            background-color: var(--sl-color-primary-50);
            color: var(--sl-color-primary-700);
        }

        .content-card {
            margin-bottom: 1rem;
            width: 100%;
        }

        .logo {
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--sl-color-primary-600);
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">English App</div>
            </div>
            <div class="sidebar-content">
                <nav>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('overview')"
                            :class="{ 'active': activeSection === 'overview' }">
                            <sl-icon name="pie-chart" slot="prefix"></sl-icon>
                            学习概况
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('pronunciation')"
                            :class="{ 'active': activeSection === 'pronunciation' }">
                            <sl-icon name="mic" slot="prefix"></sl-icon>
                            基础发音
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('words')"
                            :class="{ 'active': activeSection === 'words' }">
                            <sl-icon name="book" slot="prefix"></sl-icon>
                            单词专项
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('phrases')"
                            :class="{ 'active': activeSection === 'phrases' }">
                            <sl-icon name="collection" slot="prefix"></sl-icon>
                            短语专项
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('expressions')"
                            :class="{ 'active': activeSection === 'expressions' }">
                            <sl-icon name="chat-dots" slot="prefix"></sl-icon>
                            常用语句
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('grammar')"
                            :class="{ 'active': activeSection === 'grammar' }">
                            <sl-icon name="code-square" slot="prefix"></sl-icon>
                            语法专项
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('patterns')"
                            :class="{ 'active': activeSection === 'patterns' }">
                            <sl-icon name="list-check" slot="prefix"></sl-icon>
                            句型句式
                        </sl-button>
                    </div>
                    <div class="nav-item">
                        <sl-button variant="text" size="medium" @click="setActiveSection('settings')"
                            :class="{ 'active': activeSection === 'settings' }">
                            <sl-icon name="gear" slot="prefix"></sl-icon>
                            应用设置
                        </sl-button>
                    </div>
                </nav>
            </div>
        </div>

        <!-- 右侧主要内容区 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <div class="toolbar">
                <sl-button variant="primary" size="small">
                    <sl-icon name="plus" slot="prefix"></sl-icon>
                    新建
                </sl-button>
                <sl-button variant="default" size="small">
                    <sl-icon name="upload" slot="prefix"></sl-icon>
                    导入
                </sl-button>
                <sl-button variant="default" size="small">
                    <sl-icon name="download" slot="prefix"></sl-icon>
                    导出
                </sl-button>
                <div style="flex: 1;"></div>
                <sl-input placeholder="搜索..." size="small" pill style="width: 200px;" v-model="searchQuery"></sl-input>
                <sl-button variant="default" size="small" circle>
                    <sl-icon name="bell"></sl-icon>
                </sl-button>
                <sl-dropdown distance="6">
                    <sl-button slot="trigger" variant="default" size="small" circle>
                        <sl-icon name="person-circle"></sl-icon>
                    </sl-button>
                    <sl-menu>
                        <sl-menu-item>个人资料</sl-menu-item>
                        <sl-menu-item>账户设置</sl-menu-item>
                        <sl-divider></sl-divider>
                        <sl-menu-item>退出登录</sl-menu-item>
                    </sl-menu>
                </sl-dropdown>
            </div>

            <!-- 内容展示区 -->
            <div class="content-area">
                <!-- 学习概况内容 -->
                <div v-if="activeSection === 'overview'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>欢迎使用英语学习应用</h2>
                        </div>
                        <p>这是一个为中国少儿英语学习开发的专业应用。</p>
                    </sl-card>

                    <full-calendar shadow options='{
    "headerToolbar": {
      "left": "prev,next today",
      "center": "title",
      "right": "dayGridMonth,dayGridWeek,dayGridDay"
    }
  }' />
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                        <sl-card>
                            <div slot="header">学习统计</div>
                            <sl-progress-bar value="75" label="今日进度"></sl-progress-bar>
                            <br>
                            <p>今日已学习 15/20 个单词</p>
                        </sl-card>

                        <sl-card>
                            <div slot="header">词汇量</div>
                            <h3 style="color: var(--sl-color-primary-600);">1,250</h3>
                            <p>已掌握单词数量</p>
                        </sl-card>

                        <sl-card>
                            <div slot="header">连续学习</div>
                            <h3 style="color: var(--sl-color-success-600);">7 天</h3>
                            <p>保持学习习惯</p>
                        </sl-card>
                    </div>
                </div>

                <!-- 基础发音内容 -->
                <div v-if="activeSection === 'pronunciation'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>基础发音</h2>
                        </div>
                        <p>学习英语音标、发音规则和语音技巧。</p>
                        <br>
                        <sl-button variant="primary">开始发音练习</sl-button>
                    </sl-card>
                </div>

                <!-- 单词专项内容 -->
                <div v-if="activeSection === 'words'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>单词专项</h2>
                        </div>
                        <p>系统化学习英语单词，包括词汇记忆、拼写和用法。</p>
                        <br>
                        <sl-table>
                            <table>
                                <thead>
                                    <tr>
                                        <th>单词</th>
                                        <th>释义</th>
                                        <th>难度</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>apple</td>
                                        <td>苹果</td>
                                        <td><sl-badge variant="success">简单</sl-badge></td>
                                        <td><sl-badge variant="primary">已掌握</sl-badge></td>
                                        <td>
                                            <sl-button size="small" variant="text">编辑</sl-button>
                                            <sl-button size="small" variant="text">删除</sl-button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>beautiful</td>
                                        <td>美丽的</td>
                                        <td><sl-badge variant="warning">中等</sl-badge></td>
                                        <td><sl-badge variant="neutral">学习中</sl-badge></td>
                                        <td>
                                            <sl-button size="small" variant="text">编辑</sl-button>
                                            <sl-button size="small" variant="text">删除</sl-button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </sl-table>
                    </sl-card>
                </div>

                <!-- 短语专项内容 -->
                <div v-if="activeSection === 'phrases'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>短语专项</h2>
                        </div>
                        <p>学习常用英语短语和固定搭配。</p>
                    </sl-card>
                </div>

                <!-- 常用语句内容 -->
                <div v-if="activeSection === 'expressions'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>常用语句</h2>
                        </div>
                        <p>掌握日常交流中的常用表达和句子。</p>
                    </sl-card>
                </div>

                <!-- 语法专项内容 -->
                <div v-if="activeSection === 'grammar'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>语法专项</h2>
                        </div>
                        <p>系统学习英语语法规则和语法结构。</p>
                    </sl-card>
                </div>

                <!-- 句型句式内容 -->
                <div v-if="activeSection === 'patterns'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>句型句式</h2>
                        </div>
                        <p>学习各种英语句型结构和表达模式。</p>
                    </sl-card>
                </div>

                <!-- 应用设置内容 -->
                <div v-if="activeSection === 'settings'">
                    <sl-card class="content-card">
                        <div slot="header">
                            <h2>应用设置</h2>
                        </div>
                        <p>配置应用设置和个人学习偏好。</p>
                    </sl-card>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            compilerOptions: {
                isCustomElement: (tag) => tag.startsWith('sl-')
            },
            data() {
                return {
                    activeSection: 'overview',
                    searchQuery: ''
                }
            },
            methods: {
                setActiveSection(section) {
                    this.activeSection = section;
                }
            }
        }).mount('#app');
    </script>
</body>

</html>